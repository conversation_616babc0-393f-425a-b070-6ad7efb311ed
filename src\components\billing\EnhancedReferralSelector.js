import React, { useState, useEffect } from 'react';
import {
  Form,
  Row,
  Col,
  Card,
  Badge,
  Al<PERSON>,
  Spinner
} from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUserMd,
  faHospital,
  faFlask,
  faBuilding,
  faShieldAlt,
  faUsers,
  faInfoCircle,
  faDollarSign
} from '@fortawesome/free-solid-svg-icons';
import { referralMasterService } from '../../services/referralMasterService';

const EnhancedReferralSelector = ({ 
  selectedCategory, 
  selectedReferral, 
  onCategoryChange, 
  onReferralChange,
  disabled = false 
}) => {
  // State management
  const [referralCategories, setReferralCategories] = useState([]);
  const [referralEntities, setReferralEntities] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedReferralDetails, setSelectedReferralDetails] = useState(null);

  // Category configurations
  const categoryConfig = {
    self: { 
      title: 'Self', 
      icon: faUsers, 
      color: 'secondary',
      description: 'Direct patient payment'
    },
    labMaster: { 
      title: 'Lab', 
      icon: faFlask, 
      color: 'primary',
      description: 'Laboratory referrals'
    },
    doctorMaster: { 
      title: 'Doctor', 
      icon: faUserMd, 
      color: 'success',
      description: 'Doctor referrals'
    },
    hospitalMaster: { 
      title: 'Hospital', 
      icon: faHospital, 
      color: 'info',
      description: 'Hospital referrals'
    },
    corporateMaster: { 
      title: 'Corporate', 
      icon: faBuilding, 
      color: 'warning',
      description: 'Corporate contracts'
    },
    insuranceMaster: { 
      title: 'Insurance', 
      icon: faShieldAlt, 
      color: 'danger',
      description: 'Insurance providers'
    },
    patientMaster: { 
      title: 'Patient Category', 
      icon: faUsers, 
      color: 'dark',
      description: 'Special patient categories'
    }
  };

  // Load referral categories on mount
  useEffect(() => {
    loadReferralCategories();
  }, []);

  // Load entities when category changes
  useEffect(() => {
    if (selectedCategory && selectedCategory !== 'self') {
      loadReferralEntities(selectedCategory);
    } else {
      setReferralEntities([]);
      setSelectedReferralDetails(null);
    }
  }, [selectedCategory]);

  // Load referral details when specific referral is selected
  useEffect(() => {
    if (selectedReferral && selectedReferral !== 'self') {
      loadReferralDetails(selectedReferral);
    } else {
      setSelectedReferralDetails(null);
    }
  }, [selectedReferral]);

  const loadReferralCategories = async () => {
    try {
      const categories = [
        { id: 'self', ...categoryConfig.self },
        { id: 'labMaster', ...categoryConfig.labMaster },
        { id: 'doctorMaster', ...categoryConfig.doctorMaster },
        { id: 'hospitalMaster', ...categoryConfig.hospitalMaster },
        { id: 'corporateMaster', ...categoryConfig.corporateMaster },
        { id: 'insuranceMaster', ...categoryConfig.insuranceMaster },
        { id: 'patientMaster', ...categoryConfig.patientMaster }
      ];
      setReferralCategories(categories);
    } catch (error) {
      console.error('Error loading referral categories:', error);
    }
  };

  const loadReferralEntities = async (category) => {
    setLoading(true);
    try {
      const entities = await referralMasterService.getReferralsByCategory(category);
      const activeEntities = entities.filter(entity => entity.isActive);
      setReferralEntities(activeEntities);
    } catch (error) {
      console.error('Error loading referral entities:', error);
      setReferralEntities([]);
    } finally {
      setLoading(false);
    }
  };

  const loadReferralDetails = async (referralId) => {
    try {
      const details = await referralMasterService.getReferralById(referralId);
      setSelectedReferralDetails(details);
    } catch (error) {
      console.error('Error loading referral details:', error);
      setSelectedReferralDetails(null);
    }
  };

  const handleCategoryChange = (e) => {
    const category = e.target.value;
    onCategoryChange(category);
    
    // Reset referral selection when category changes
    onReferralChange('');
  };

  const handleReferralChange = (e) => {
    const referralId = e.target.value;
    onReferralChange(referralId);
  };

  const getCategoryIcon = (categoryId) => {
    const config = categoryConfig[categoryId];
    return config ? config.icon : faUsers;
  };

  const getCategoryColor = (categoryId) => {
    const config = categoryConfig[categoryId];
    return config ? config.color : 'secondary';
  };

  return (
    <div className="enhanced-referral-selector">
      <Row>
        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>
              <FontAwesomeIcon icon={faUsers} className="me-2" />
              Referral Category *
            </Form.Label>
            <Form.Select
              value={selectedCategory}
              onChange={handleCategoryChange}
              disabled={disabled}
              required
            >
              <option value="">Select Referral Category</option>
              {referralCategories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.title} - {category.description}
                </option>
              ))}
            </Form.Select>
          </Form.Group>
        </Col>

        <Col md={6}>
          <Form.Group className="mb-3">
            <Form.Label>
              {selectedCategory && (
                <FontAwesomeIcon icon={getCategoryIcon(selectedCategory)} className="me-2" />
              )}
              Specific Referral {selectedCategory && selectedCategory !== 'self' ? '*' : ''}
            </Form.Label>
            <Form.Select
              value={selectedReferral}
              onChange={handleReferralChange}
              disabled={disabled || !selectedCategory || selectedCategory === 'self'}
              required={selectedCategory && selectedCategory !== 'self'}
            >
              <option value="">
                {selectedCategory === 'self' 
                  ? 'Self Payment - No specific referral needed'
                  : loading 
                    ? 'Loading...'
                    : referralEntities.length === 0
                      ? 'No entities available'
                      : 'Select Specific Referral'
                }
              </option>
              {referralEntities.map(entity => (
                <option key={entity.id} value={`${selectedCategory}_${entity.id}`}>
                  {entity.name} ({entity.code})
                  {entity.priceScheme && ` - ${entity.priceScheme}`}
                  {entity.discountPercentage > 0 && ` (${entity.discountPercentage}% discount)`}
                </option>
              ))}
            </Form.Select>
            {loading && (
              <div className="text-center mt-2">
                <Spinner animation="border" size="sm" />
              </div>
            )}
          </Form.Group>
        </Col>
      </Row>

      {/* Referral Details Card */}
      {selectedReferralDetails && (
        <Card className="mt-3 border-start border-4" style={{ borderLeftColor: `var(--bs-${getCategoryColor(selectedCategory)})` }}>
          <Card.Body className="py-2">
            <Row className="align-items-center">
              <Col md={8}>
                <div className="d-flex align-items-center mb-2">
                  <FontAwesomeIcon 
                    icon={getCategoryIcon(selectedCategory)} 
                    className={`me-2 text-${getCategoryColor(selectedCategory)}`}
                  />
                  <strong>{selectedReferralDetails.name}</strong>
                  <Badge bg={getCategoryColor(selectedCategory)} className="ms-2">
                    {selectedReferralDetails.categoryTitle}
                  </Badge>
                </div>
                
                <div className="small text-muted">
                  {selectedReferralDetails.description && (
                    <div>{selectedReferralDetails.description}</div>
                  )}
                  {selectedReferralDetails.contactPerson && (
                    <div>Contact: {selectedReferralDetails.contactPerson}</div>
                  )}
                  {selectedReferralDetails.contactNumber && (
                    <div>Phone: {selectedReferralDetails.contactNumber}</div>
                  )}
                </div>
              </Col>
              
              <Col md={4} className="text-end">
                <div className="mb-1">
                  {selectedReferralDetails.priceScheme ? (
                    <Badge bg="success" className="me-1">
                      <FontAwesomeIcon icon={faDollarSign} className="me-1" />
                      {selectedReferralDetails.priceScheme}
                    </Badge>
                  ) : (
                    <Badge bg="secondary" className="me-1">No Price Scheme</Badge>
                  )}
                </div>
                
                {selectedReferralDetails.discountPercentage > 0 && (
                  <div className="small text-success">
                    <strong>{selectedReferralDetails.discountPercentage}% Discount</strong>
                  </div>
                )}
                
                {selectedReferralDetails.commissionPercentage > 0 && (
                  <div className="small text-info">
                    {selectedReferralDetails.commissionPercentage}% Commission
                  </div>
                )}
              </Col>
            </Row>
          </Card.Body>
        </Card>
      )}

      {/* Self Payment Info */}
      {selectedCategory === 'self' && (
        <Alert variant="info" className="mt-3">
          <FontAwesomeIcon icon={faInfoCircle} className="me-2" />
          <strong>Self Payment:</strong> Standard pricing will be applied. No referral discounts or special schemes.
        </Alert>
      )}
    </div>
  );
};

export default EnhancedReferralSelector;
