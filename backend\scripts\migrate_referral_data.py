#!/usr/bin/env python3
"""
Referral Master Data Migration Script

This script helps migrate existing referral data to the new master data format
and sets up initial data structures for the referral management system.
"""

import json
import os
import sys
from datetime import datetime

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def read_json_file(file_path):
    """Read JSON file and return data"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return json.load(file)
    except FileNotFoundError:
        print(f"File not found: {file_path}")
        return None
    except json.JSONDecodeError as e:
        print(f"Error reading JSON file {file_path}: {e}")
        return None

def write_json_file(file_path, data):
    """Write data to JSON file"""
    try:
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as file:
            json.dump(data, file, indent=2, ensure_ascii=False)
        print(f"Successfully wrote data to {file_path}")
        return True
    except Exception as e:
        print(f"Error writing to file {file_path}: {e}")
        return False

def migrate_existing_referral_data():
    """Migrate existing referral data from referralPricingMaster.json"""
    
    # Path to existing referral data
    existing_data_path = os.path.join(os.path.dirname(__file__), '..', 'data', 'referralPricingMaster.json')
    existing_data = read_json_file(existing_data_path)
    
    if not existing_data or 'referralMaster' not in existing_data:
        print("No existing referral data found to migrate")
        return False
    
    # Initialize category data
    migrated_data = {
        'labMaster': [],
        'doctorMaster': [],
        'hospitalMaster': [],
        'corporateMaster': [],
        'insuranceMaster': [],
        'patientMaster': []
    }
    
    # Migrate existing referral master data
    for ref_id, ref_data in existing_data['referralMaster'].items():
        category = ref_data.get('category', 'patientMaster')
        
        # Map category names to our master data categories
        category_mapping = {
            'medical': 'doctorMaster',
            'hospital': 'hospitalMaster',
            'corporate': 'corporateMaster',
            'insurance': 'insuranceMaster',
            'lab': 'labMaster',
            'patient': 'patientMaster'
        }
        
        target_category = category_mapping.get(category, 'patientMaster')
        
        # Create migrated record
        migrated_record = {
            'id': len(migrated_data[target_category]) + 1,
            'code': ref_data.get('id', f"MIG{len(migrated_data[target_category]) + 1:03d}"),
            'name': ref_data.get('name', 'Unknown'),
            'description': ref_data.get('description', ''),
            'contactPerson': '',
            'contactNumber': '',
            'email': '',
            'address': '',
            'city': '',
            'state': '',
            'pincode': '',
            'gstNumber': '',
            'panNumber': '',
            'priceScheme': ref_data.get('defaultPricingScheme', ''),
            'discountPercentage': ref_data.get('discountPercentage', 0),
            'commissionPercentage': ref_data.get('commissionPercentage', 0),
            'creditLimit': 0,
            'creditDays': 0,
            'isActive': ref_data.get('isActive', True),
            'remarks': f"Migrated from existing referral data - {ref_data.get('id', '')}",
            'created_at': ref_data.get('createdAt', datetime.now().isoformat()),
            'updated_at': ref_data.get('updatedAt', datetime.now().isoformat()),
            'created_by': ref_data.get('createdBy', 1)
        }
        
        # Add category-specific fields
        if target_category == 'doctorMaster':
            migrated_record.update({
                'qualification': '',
                'specialization': '',
                'registrationNumber': '',
                'consultationFee': 0
            })
        elif target_category == 'hospitalMaster':
            migrated_record.update({
                'hospitalType': '',
                'bedCapacity': 0,
                'accreditation': '',
                'emergencyServices': False
            })
        elif target_category == 'corporateMaster':
            migrated_record.update({
                'companyType': '',
                'employeeCount': 0,
                'industry': '',
                'contractStartDate': '',
                'contractEndDate': ''
            })
        elif target_category == 'insuranceMaster':
            migrated_record.update({
                'insuranceType': '',
                'policyNumber': '',
                'coverageAmount': 0,
                'validFrom': '',
                'validTo': ''
            })
        elif target_category == 'labMaster':
            migrated_record.update({
                'labType': '',
                'accreditation': '',
                'testCapabilities': [],
                'equipmentList': []
            })
        
        migrated_data[target_category].append(migrated_record)
    
    # Write migrated data to individual category files
    data_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
    success_count = 0
    
    for category, records in migrated_data.items():
        if records:  # Only write if there are records
            file_path = os.path.join(data_dir, f"{category}.json")
            if write_json_file(file_path, records):
                success_count += 1
                print(f"Migrated {len(records)} records to {category}")
    
    print(f"\nMigration completed. {success_count} category files created.")
    return success_count > 0

def create_sample_data():
    """Create sample data for testing"""
    
    print("Creating sample referral master data...")
    
    # Sample data is already created in the individual JSON files
    # This function can be used to verify or recreate sample data
    
    data_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
    categories = ['labMaster', 'doctorMaster', 'hospitalMaster', 'corporateMaster', 'insuranceMaster', 'patientMaster']
    
    for category in categories:
        file_path = os.path.join(data_dir, f"{category}.json")
        if os.path.exists(file_path):
            data = read_json_file(file_path)
            if data:
                print(f"✓ {category}: {len(data)} records found")
            else:
                print(f"✗ {category}: No data found")
        else:
            print(f"✗ {category}: File not found")

def validate_data_integrity():
    """Validate the integrity of referral master data"""
    
    print("\nValidating referral master data integrity...")
    
    data_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
    categories = ['labMaster', 'doctorMaster', 'hospitalMaster', 'corporateMaster', 'insuranceMaster', 'patientMaster']
    
    total_records = 0
    validation_errors = []
    
    for category in categories:
        file_path = os.path.join(data_dir, f"{category}.json")
        data = read_json_file(file_path)
        
        if not data:
            validation_errors.append(f"{category}: No data found")
            continue
        
        total_records += len(data)
        
        # Validate required fields
        for i, record in enumerate(data):
            required_fields = ['id', 'code', 'name', 'isActive']
            for field in required_fields:
                if field not in record:
                    validation_errors.append(f"{category}[{i}]: Missing required field '{field}'")
            
            # Validate data types
            if 'discountPercentage' in record and not isinstance(record['discountPercentage'], (int, float)):
                validation_errors.append(f"{category}[{i}]: discountPercentage must be a number")
            
            if 'isActive' in record and not isinstance(record['isActive'], bool):
                validation_errors.append(f"{category}[{i}]: isActive must be a boolean")
    
    print(f"Total records validated: {total_records}")
    
    if validation_errors:
        print(f"\nValidation errors found ({len(validation_errors)}):")
        for error in validation_errors[:10]:  # Show first 10 errors
            print(f"  - {error}")
        if len(validation_errors) > 10:
            print(f"  ... and {len(validation_errors) - 10} more errors")
        return False
    else:
        print("✓ All data validation checks passed!")
        return True

def main():
    """Main migration function"""
    
    print("=== Referral Master Data Migration ===\n")
    
    # Check if we should migrate existing data
    migrate_choice = input("Do you want to migrate existing referral data? (y/n): ").lower().strip()
    
    if migrate_choice == 'y':
        print("\nMigrating existing referral data...")
        migrate_existing_referral_data()
    
    # Create/verify sample data
    print("\nVerifying sample data...")
    create_sample_data()
    
    # Validate data integrity
    validate_data_integrity()
    
    print("\n=== Migration Complete ===")
    print("\nNext steps:")
    print("1. Review the generated data files in backend/data/")
    print("2. Update any missing information in the master data files")
    print("3. Test the referral master data functionality in the application")
    print("4. Configure price schemes for referral entities as needed")

if __name__ == "__main__":
    main()
