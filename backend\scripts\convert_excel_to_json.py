#!/usr/bin/env python3
"""
Convert Excel data to JSON format for the referral master data system
"""

import json
import os
import sys
import re

def parse_price_scheme_html(file_path):
    """Parse the PriceScheme.xls HTML table and convert to JSON using regex"""

    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # Find all table rows using regex
        row_pattern = r'<tr>(.*?)</tr>'
        rows = re.findall(row_pattern, content, re.DOTALL)

        if len(rows) < 2:
            print("No data rows found")
            return []

        # Parse header row
        header_row = rows[0]
        cell_pattern = r'<td>(.*?)</td>'
        headers = [cell.strip() for cell in re.findall(cell_pattern, header_row)]
        print(f"Headers found: {headers}")

        # Parse data rows
        price_scheme_data = []
        for row in rows[1:]:  # Skip header row
            cells = re.findall(cell_pattern, row)
            if len(cells) >= len(headers):
                row_data = {}
                for i, header in enumerate(headers):
                    if i < len(cells):
                        cell_value = cells[i].strip()

                        # Convert numeric values
                        if header in ['Test_Amount', 'Old_Amount', 'Spl_Amount']:
                            try:
                                row_data[header] = float(cell_value) if cell_value else 0
                            except ValueError:
                                row_data[header] = 0
                        else:
                            row_data[header] = cell_value

                price_scheme_data.append(row_data)

        print(f"Parsed {len(price_scheme_data)} price scheme records")
        return price_scheme_data

    except Exception as e:
        print(f"Error parsing price scheme file: {e}")
        return []

def create_excel_data_json():
    """Create the excel_data.json file with PriceScheme data"""
    
    # Get the path to PriceScheme.xls
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(script_dir))
    price_scheme_file = os.path.join(project_root, 'PriceScheme.xls')
    
    if not os.path.exists(price_scheme_file):
        print(f"PriceScheme.xls not found at: {price_scheme_file}")
        return False
    
    print(f"Reading PriceScheme data from: {price_scheme_file}")
    
    # Parse the price scheme data
    price_scheme_data = parse_price_scheme_html(price_scheme_file)
    
    if not price_scheme_data:
        print("No price scheme data found")
        return False
    
    # Create the excel_data structure
    excel_data = {
        "PriceScheme": price_scheme_data,
        "metadata": {
            "source": "PriceScheme.xls",
            "total_records": len(price_scheme_data),
            "schemes": list(set(item.get('Scheme_Code', '') for item in price_scheme_data if item.get('Scheme_Code'))),
            "departments": list(set(item.get('Dept_Code', '') for item in price_scheme_data if item.get('Dept_Code'))),
            "generated_at": "2025-01-01T00:00:00Z"
        }
    }
    
    # Write to excel_data.json
    data_dir = os.path.join(os.path.dirname(script_dir), 'data')
    os.makedirs(data_dir, exist_ok=True)
    
    output_file = os.path.join(data_dir, 'excel_data.json')
    
    try:
        with open(output_file, 'w', encoding='utf-8') as file:
            json.dump(excel_data, file, indent=2, ensure_ascii=False)
        
        print(f"Successfully created excel_data.json at: {output_file}")
        print(f"Total records: {len(price_scheme_data)}")
        print(f"Unique schemes: {len(excel_data['metadata']['schemes'])}")
        print(f"Schemes found: {excel_data['metadata']['schemes']}")
        
        return True
        
    except Exception as e:
        print(f"Error writing excel_data.json: {e}")
        return False

def validate_l2l_scheme():
    """Validate that L2L scheme is properly included"""
    
    data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data')
    excel_data_file = os.path.join(data_dir, 'excel_data.json')
    
    if not os.path.exists(excel_data_file):
        print("excel_data.json not found")
        return False
    
    try:
        with open(excel_data_file, 'r', encoding='utf-8') as file:
            excel_data = json.load(file)
        
        price_scheme_data = excel_data.get('PriceScheme', [])
        
        # Find L2L scheme records
        l2l_records = [record for record in price_scheme_data if record.get('Scheme_Code') == '@000002']
        
        print(f"\nL2L Scheme Validation:")
        print(f"Total L2L records found: {len(l2l_records)}")
        
        if l2l_records:
            print("Sample L2L records:")
            for i, record in enumerate(l2l_records[:5]):  # Show first 5
                print(f"  {i+1}. {record.get('Test_Name', 'N/A')} - ₹{record.get('Test_Amount', 0)}")
            
            if len(l2l_records) > 5:
                print(f"  ... and {len(l2l_records) - 5} more records")
            
            return True
        else:
            print("No L2L scheme records found!")
            return False
            
    except Exception as e:
        print(f"Error validating L2L scheme: {e}")
        return False

def main():
    """Main conversion function"""
    
    print("=== Excel to JSON Conversion ===\n")
    
    # Create excel_data.json
    if create_excel_data_json():
        print("\n✓ Excel data conversion completed successfully")
        
        # Validate L2L scheme
        if validate_l2l_scheme():
            print("✓ L2L scheme validation passed")
        else:
            print("✗ L2L scheme validation failed")
    else:
        print("\n✗ Excel data conversion failed")
        return False
    
    print("\n=== Conversion Complete ===")
    print("\nNext steps:")
    print("1. Restart the backend server to load the new excel_data.json")
    print("2. Test the price scheme assignment functionality")
    print("3. Verify L2L scheme appears in the price scheme dropdown")
    
    return True

if __name__ == "__main__":
    main()
