import { adminAPI } from './api';

class ReferralMasterService {
  constructor() {
    this.referralData = null;
    this.priceSchemes = null;
    this.lastFetch = null;
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Get all referral entities from master data
   */
  async getAllReferralEntities() {
    try {
      // Check cache
      if (this.referralData && this.lastFetch && 
          (Date.now() - this.lastFetch) < this.cacheTimeout) {
        return this.referralData;
      }

      const response = await adminAPI.get('/admin/referral-master-data');
      if (response.data.success) {
        this.referralData = response.data.data;
        this.lastFetch = Date.now();
        return this.referralData;
      } else {
        throw new Error('Failed to fetch referral master data');
      }
    } catch (error) {
      console.error('Error fetching referral entities:', error);
      // Return empty structure if API fails
      return {
        labMaster: [],
        doctorMaster: [],
        hospitalMaster: [],
        corporateMaster: [],
        insuranceMaster: [],
        patientMaster: []
      };
    }
  }

  /**
   * Get formatted referral sources for dropdown selection
   */
  async getReferralSourcesForDropdown() {
    try {
      const referralData = await this.getAllReferralEntities();
      const formattedSources = [];

      // Add default self option
      formattedSources.push({
        id: 'self',
        name: 'Self',
        description: 'Direct patient payment',
        category: 'self',
        priceScheme: '',
        discountPercentage: 0,
        commissionPercentage: 0,
        isActive: true
      });

      // Process each category
      Object.entries(referralData).forEach(([category, items]) => {
        items.forEach(item => {
          if (item.isActive) {
            formattedSources.push({
              id: `${category}_${item.id}`,
              name: item.name,
              description: item.description || `${this.getCategoryTitle(category)} referral`,
              category: category,
              categoryTitle: this.getCategoryTitle(category),
              code: item.code,
              priceScheme: item.priceScheme || '',
              discountPercentage: item.discountPercentage || 0,
              commissionPercentage: item.commissionPercentage || 0,
              contactPerson: item.contactPerson,
              contactNumber: item.contactNumber,
              isActive: item.isActive,
              originalId: item.id
            });
          }
        });
      });

      return formattedSources;
    } catch (error) {
      console.error('Error formatting referral sources:', error);
      return [{
        id: 'self',
        name: 'Self',
        description: 'Direct patient payment',
        category: 'self',
        priceScheme: '',
        discountPercentage: 0,
        commissionPercentage: 0,
        isActive: true
      }];
    }
  }

  /**
   * Get price schemes for referral
   */
  async getPriceSchemes() {
    try {
      if (this.priceSchemes && this.lastFetch && 
          (Date.now() - this.lastFetch) < this.cacheTimeout) {
        return this.priceSchemes;
      }

      const response = await adminAPI.get('/admin/price-schemes-for-referral');
      if (response.data.success) {
        this.priceSchemes = response.data.data;
        return this.priceSchemes;
      } else {
        throw new Error('Failed to fetch price schemes');
      }
    } catch (error) {
      console.error('Error fetching price schemes:', error);
      return [];
    }
  }

  /**
   * Get pricing for a specific referral entity
   */
  async getReferralPricing(referralId) {
    try {
      // Extract category and original ID from composite ID
      const [category, originalId] = referralId.includes('_') ? 
        referralId.split('_') : ['self', referralId];

      if (category === 'self') {
        return {
          referralId: 'self',
          referralName: 'Self',
          category: 'self',
          priceScheme: '',
          discountPercentage: 0,
          commissionPercentage: 0,
          testPrices: []
        };
      }

      const response = await adminAPI.get(`/admin/referral-pricing/${originalId}`);
      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error('Failed to fetch referral pricing');
      }
    } catch (error) {
      console.error('Error fetching referral pricing:', error);
      return {
        referralId: referralId,
        referralName: 'Unknown',
        category: 'unknown',
        priceScheme: '',
        discountPercentage: 0,
        commissionPercentage: 0,
        testPrices: []
      };
    }
  }

  /**
   * Calculate test price based on referral and price scheme
   */
  async calculateTestPrice(testId, referralId, basePrice = 0) {
    try {
      const referralPricing = await this.getReferralPricing(referralId);
      
      // If no price scheme assigned, use base price with referral discount
      if (!referralPricing.priceScheme) {
        const discountAmount = (basePrice * referralPricing.discountPercentage) / 100;
        const finalPrice = basePrice - discountAmount;
        
        return {
          price: Math.max(0, finalPrice),
          basePrice: basePrice,
          discountPercentage: referralPricing.discountPercentage,
          discountAmount: discountAmount,
          priceScheme: '',
          source: 'referral_discount',
          details: `Base price with ${referralPricing.discountPercentage}% referral discount`
        };
      }

      // Look for specific test price in the price scheme
      const testPrice = referralPricing.testPrices.find(tp => tp.testCode === testId);
      
      if (testPrice) {
        // Use scheme-specific price
        const schemePrice = parseFloat(testPrice.testAmount) || basePrice;
        const additionalDiscount = (schemePrice * referralPricing.discountPercentage) / 100;
        const finalPrice = schemePrice - additionalDiscount;
        
        return {
          price: Math.max(0, finalPrice),
          basePrice: basePrice,
          schemePrice: schemePrice,
          discountPercentage: referralPricing.discountPercentage,
          discountAmount: additionalDiscount,
          priceScheme: referralPricing.priceScheme,
          source: 'price_scheme',
          details: `Price scheme ${referralPricing.priceScheme} with ${referralPricing.discountPercentage}% additional discount`
        };
      } else {
        // Use base price with referral discount
        const discountAmount = (basePrice * referralPricing.discountPercentage) / 100;
        const finalPrice = basePrice - discountAmount;
        
        return {
          price: Math.max(0, finalPrice),
          basePrice: basePrice,
          discountPercentage: referralPricing.discountPercentage,
          discountAmount: discountAmount,
          priceScheme: referralPricing.priceScheme,
          source: 'base_with_discount',
          details: `Base price with ${referralPricing.discountPercentage}% referral discount (test not in price scheme)`
        };
      }
    } catch (error) {
      console.error('Error calculating test price:', error);
      return {
        price: basePrice,
        basePrice: basePrice,
        discountPercentage: 0,
        discountAmount: 0,
        priceScheme: '',
        source: 'fallback',
        details: 'Fallback to base price due to calculation error'
      };
    }
  }

  /**
   * Get category title for display
   */
  getCategoryTitle(category) {
    const titles = {
      labMaster: 'Lab',
      doctorMaster: 'Doctor',
      hospitalMaster: 'Hospital',
      corporateMaster: 'Corporate',
      insuranceMaster: 'Insurance',
      patientMaster: 'Patient'
    };
    return titles[category] || category;
  }

  /**
   * Clear cache to force refresh
   */
  clearCache() {
    this.referralData = null;
    this.priceSchemes = null;
    this.lastFetch = null;
  }

  /**
   * Get referral entity by ID
   */
  async getReferralById(referralId) {
    try {
      const sources = await this.getReferralSourcesForDropdown();
      return sources.find(source => source.id === referralId);
    } catch (error) {
      console.error('Error getting referral by ID:', error);
      return null;
    }
  }

  /**
   * Get referrals by category
   */
  async getReferralsByCategory(category) {
    try {
      const referralData = await this.getAllReferralEntities();
      return referralData[category] || [];
    } catch (error) {
      console.error('Error getting referrals by category:', error);
      return [];
    }
  }

  /**
   * Get enhanced referral sources with category information for cascading dropdowns
   */
  async getEnhancedReferralSources() {
    try {
      const referralData = await this.getAllReferralEntities();
      const enhancedSources = {
        categories: [
          { id: 'self', title: 'Self', description: 'Direct patient payment' },
          { id: 'labMaster', title: 'Lab', description: 'Laboratory referrals' },
          { id: 'doctorMaster', title: 'Doctor', description: 'Doctor referrals' },
          { id: 'hospitalMaster', title: 'Hospital', description: 'Hospital referrals' },
          { id: 'corporateMaster', title: 'Corporate', description: 'Corporate contracts' },
          { id: 'insuranceMaster', title: 'Insurance', description: 'Insurance providers' },
          { id: 'patientMaster', title: 'Patient Category', description: 'Special patient categories' }
        ],
        entities: {}
      };

      // Organize entities by category
      Object.entries(referralData).forEach(([category, items]) => {
        enhancedSources.entities[category] = items
          .filter(item => item.isActive)
          .map(item => ({
            id: `${category}_${item.id}`,
            originalId: item.id,
            category: category,
            categoryTitle: this.getCategoryTitle(category),
            code: item.code,
            name: item.name,
            description: item.description,
            contactPerson: item.contactPerson,
            contactNumber: item.contactNumber,
            email: item.email,
            priceScheme: item.priceScheme || '',
            discountPercentage: item.discountPercentage || 0,
            commissionPercentage: item.commissionPercentage || 0,
            isActive: item.isActive
          }));
      });

      return enhancedSources;
    } catch (error) {
      console.error('Error getting enhanced referral sources:', error);
      return {
        categories: [{ id: 'self', title: 'Self', description: 'Direct patient payment' }],
        entities: {}
      };
    }
  }

  /**
   * Calculate comprehensive test pricing with detailed breakdown
   */
  async calculateComprehensiveTestPrice(testId, referralId, basePrice = 0) {
    try {
      const referralPricing = await this.getReferralPricing(referralId);

      // Initialize pricing breakdown
      const pricingBreakdown = {
        testId: testId,
        referralId: referralId,
        referralName: referralPricing.referralName,
        category: referralPricing.category,
        basePrice: basePrice,
        schemePrice: basePrice,
        priceScheme: referralPricing.priceScheme,
        discountPercentage: referralPricing.discountPercentage,
        discountAmount: 0,
        finalPrice: basePrice,
        calculations: [],
        source: 'base'
      };

      // If no price scheme assigned, use base price with referral discount
      if (!referralPricing.priceScheme) {
        const discountAmount = (basePrice * referralPricing.discountPercentage) / 100;
        const finalPrice = Math.max(0, basePrice - discountAmount);

        pricingBreakdown.discountAmount = discountAmount;
        pricingBreakdown.finalPrice = finalPrice;
        pricingBreakdown.source = 'referral_discount';
        pricingBreakdown.calculations.push({
          step: 'Base Price',
          amount: basePrice,
          description: 'Original test price'
        });
        pricingBreakdown.calculations.push({
          step: 'Referral Discount',
          amount: -discountAmount,
          description: `${referralPricing.discountPercentage}% referral discount`
        });
        pricingBreakdown.calculations.push({
          step: 'Final Price',
          amount: finalPrice,
          description: 'Price after referral discount'
        });

        return pricingBreakdown;
      }

      // Look for specific test price in the price scheme
      const testPrice = referralPricing.testPrices.find(tp => tp.testCode === testId);

      if (testPrice) {
        // Use scheme-specific price
        const schemePrice = parseFloat(testPrice.testAmount) || basePrice;
        const additionalDiscount = (schemePrice * referralPricing.discountPercentage) / 100;
        const finalPrice = Math.max(0, schemePrice - additionalDiscount);

        pricingBreakdown.schemePrice = schemePrice;
        pricingBreakdown.discountAmount = additionalDiscount;
        pricingBreakdown.finalPrice = finalPrice;
        pricingBreakdown.source = 'price_scheme';
        pricingBreakdown.calculations.push({
          step: 'Base Price',
          amount: basePrice,
          description: 'Original test price'
        });
        pricingBreakdown.calculations.push({
          step: 'Scheme Price',
          amount: schemePrice,
          description: `Price from scheme ${referralPricing.priceScheme}`
        });
        if (additionalDiscount > 0) {
          pricingBreakdown.calculations.push({
            step: 'Additional Discount',
            amount: -additionalDiscount,
            description: `${referralPricing.discountPercentage}% additional discount`
          });
        }
        pricingBreakdown.calculations.push({
          step: 'Final Price',
          amount: finalPrice,
          description: 'Final calculated price'
        });

        return pricingBreakdown;
      } else {
        // Use base price with referral discount
        const discountAmount = (basePrice * referralPricing.discountPercentage) / 100;
        const finalPrice = Math.max(0, basePrice - discountAmount);

        pricingBreakdown.discountAmount = discountAmount;
        pricingBreakdown.finalPrice = finalPrice;
        pricingBreakdown.source = 'base_with_discount';
        pricingBreakdown.calculations.push({
          step: 'Base Price',
          amount: basePrice,
          description: 'Original test price'
        });
        pricingBreakdown.calculations.push({
          step: 'Referral Discount',
          amount: -discountAmount,
          description: `${referralPricing.discountPercentage}% referral discount (test not in price scheme)`
        });
        pricingBreakdown.calculations.push({
          step: 'Final Price',
          amount: finalPrice,
          description: 'Price after referral discount'
        });

        return pricingBreakdown;
      }
    } catch (error) {
      console.error('Error calculating comprehensive test price:', error);
      return {
        testId: testId,
        referralId: referralId,
        referralName: 'Unknown',
        category: 'unknown',
        basePrice: basePrice,
        schemePrice: basePrice,
        priceScheme: '',
        discountPercentage: 0,
        discountAmount: 0,
        finalPrice: basePrice,
        calculations: [{
          step: 'Fallback Price',
          amount: basePrice,
          description: 'Fallback to base price due to calculation error'
        }],
        source: 'fallback'
      };
    }
  }
}

// Export singleton instance
export const referralMasterService = new ReferralMasterService();
export default referralMasterService;
