import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Table,
  Button,
  Modal,
  Form,
  Row,
  Col,
  Al<PERSON>,
  Badge,
  Spinner,
  InputGroup
} from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faDollarSign,
  faUserCog,
  faSearch,
  faSync,
  faCheck,
  faTimes,
  faInfoCircle,
  faEdit
} from '@fortawesome/free-solid-svg-icons';
import { adminAPI } from '../../services/api';

const PriceSchemeAssignment = () => {
  // State management
  const [referralData, setReferralData] = useState({
    labMaster: [],
    doctorMaster: [],
    hospitalMaster: [],
    corporateMaster: [],
    insuranceMaster: [],
    patientMaster: []
  });
  const [priceSchemes, setPriceSchemes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  
  // Modal states
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [selectedReferral, setSelectedReferral] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedScheme, setSelectedScheme] = useState('');

  // Category configurations
  const categoryConfig = {
    labMaster: { title: 'Labs', color: 'primary' },
    doctorMaster: { title: 'Doctors', color: 'success' },
    hospitalMaster: { title: 'Hospitals', color: 'info' },
    corporateMaster: { title: 'Corporate', color: 'warning' },
    insuranceMaster: { title: 'Insurance', color: 'danger' },
    patientMaster: { title: 'Patients', color: 'secondary' }
  };

  // Load data on component mount
  useEffect(() => {
    fetchAllData();
    fetchPriceSchemes();
  }, []);

  const fetchAllData = async () => {
    setLoading(true);
    try {
      const response = await adminAPI.get('/admin/referral-master-data');
      if (response.data.success) {
        setReferralData(response.data.data);
      } else {
        setError('Failed to load referral data');
      }
    } catch (err) {
      console.error('Error fetching referral data:', err);
      setError('Failed to load referral data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchPriceSchemes = async () => {
    try {
      console.log('Fetching price schemes...');
      const response = await adminAPI.get('/admin/price-schemes-for-referral');
      console.log('Price schemes API response:', response.data);

      if (response.data.success) {
        setPriceSchemes(response.data.data);
        console.log('Price schemes loaded:', response.data.data);

        // Log debug information if available
        if (response.data.debug) {
          console.log('Debug info:', response.data.debug);
        }
      } else {
        console.error('API returned error:', response.data.message);
        setError(`Failed to load price schemes: ${response.data.message}`);
      }
    } catch (err) {
      console.error('Error fetching price schemes:', err);
      setError(`Network error loading price schemes: ${err.message}`);
    }
  };

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };

  const getAllReferrals = () => {
    const allReferrals = [];
    Object.entries(referralData).forEach(([category, items]) => {
      items.forEach(item => {
        allReferrals.push({
          ...item,
          category,
          categoryTitle: categoryConfig[category].title,
          categoryColor: categoryConfig[category].color
        });
      });
    });
    return allReferrals;
  };

  const getFilteredReferrals = () => {
    const allReferrals = getAllReferrals();
    if (!searchQuery) return allReferrals;
    
    return allReferrals.filter(item =>
      item.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.code?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.categoryTitle?.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const handleAssignClick = (referral, category) => {
    setSelectedReferral(referral);
    setSelectedCategory(category);
    setSelectedScheme(referral.priceScheme || '');
    setShowAssignModal(true);
  };

  const handleAssignSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      const response = await adminAPI.post('/admin/assign-price-scheme', {
        category: selectedCategory,
        referralId: selectedReferral.id,
        priceScheme: selectedScheme
      });
      
      if (response.data.success) {
        await fetchAllData();
        setShowAssignModal(false);
        setSelectedReferral(null);
        setSelectedCategory('');
        setSelectedScheme('');
        setError('');
      } else {
        setError(response.data.message || 'Failed to assign price scheme');
      }
    } catch (err) {
      console.error('Error assigning price scheme:', err);
      setError('Failed to assign price scheme. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getSchemeName = (schemeCode) => {
    const scheme = priceSchemes.find(s => s.code === schemeCode);
    return scheme ? scheme.name : schemeCode;
  };

  const getAssignmentStats = () => {
    const allReferrals = getAllReferrals();
    const assigned = allReferrals.filter(r => r.priceScheme).length;
    const unassigned = allReferrals.length - assigned;
    
    return { total: allReferrals.length, assigned, unassigned };
  };

  const stats = getAssignmentStats();

  return (
    <div className="price-scheme-assignment">
      <Card>
        <Card.Header className="d-flex justify-content-between align-items-center">
          <div>
            <h5 className="mb-0">
              <FontAwesomeIcon icon={faDollarSign} className="me-2" />
              Price Scheme Assignment
            </h5>
            <small className="text-muted">
              Assign price schemes to referral entities for dynamic pricing
            </small>
          </div>
          <div className="d-flex gap-2">
            <Button variant="outline-primary" onClick={fetchAllData}>
              <FontAwesomeIcon icon={faSync} className="me-1" />
              Refresh
            </Button>
          </div>
        </Card.Header>

        <div className="card-header-search py-2 px-3 border-bottom">
          <Row className="align-items-center">
            <Col md={6}>
              <InputGroup>
                <Form.Control
                  type="text"
                  placeholder="Search referrals..."
                  value={searchQuery}
                  onChange={handleSearch}
                />
                <Button variant="outline-secondary">
                  <FontAwesomeIcon icon={faSearch} />
                </Button>
              </InputGroup>
            </Col>
            <Col md={6}>
              <div className="d-flex gap-3 justify-content-end">
                <div className="text-center">
                  <div className="h6 mb-0 text-success">{stats.assigned}</div>
                  <small className="text-muted">Assigned</small>
                </div>
                <div className="text-center">
                  <div className="h6 mb-0 text-warning">{stats.unassigned}</div>
                  <small className="text-muted">Unassigned</small>
                </div>
                <div className="text-center">
                  <div className="h6 mb-0 text-primary">{stats.total}</div>
                  <small className="text-muted">Total</small>
                </div>
              </div>
            </Col>
          </Row>
        </div>

        <Card.Body>
          {error && <Alert variant="danger">{error}</Alert>}
          
          <Alert variant="info" className="mb-3">
            <FontAwesomeIcon icon={faInfoCircle} className="me-2" />
            <strong>Price Scheme Assignment:</strong> Assign price schemes to referral entities to enable 
            automatic price calculation in billing based on the selected referral source.
          </Alert>

          {loading ? (
            <div className="text-center py-4">
              <Spinner animation="border" role="status">
                <span className="visually-hidden">Loading...</span>
              </Spinner>
            </div>
          ) : (
            <div className="table-responsive">
              <Table className="table-hover">
                <thead>
                  <tr>
                    <th>Category</th>
                    <th>Code</th>
                    <th>Name</th>
                    <th>Contact</th>
                    <th>Current Price Scheme</th>
                    <th>Discount %</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {getFilteredReferrals().map(referral => (
                    <tr key={`${referral.category}-${referral.id}`}>
                      <td>
                        <Badge bg={referral.categoryColor}>
                          {referral.categoryTitle}
                        </Badge>
                      </td>
                      <td>{referral.code}</td>
                      <td>
                        <div>
                          <strong>{referral.name}</strong>
                          {referral.description && (
                            <div className="text-muted small">{referral.description}</div>
                          )}
                        </div>
                      </td>
                      <td>
                        <div>
                          {referral.contactPerson && <div>{referral.contactPerson}</div>}
                          {referral.contactNumber && (
                            <div className="text-muted small">{referral.contactNumber}</div>
                          )}
                        </div>
                      </td>
                      <td>
                        {referral.priceScheme ? (
                          <div>
                            <Badge bg="success" className="mb-1">
                              {referral.priceScheme}
                            </Badge>
                            <div className="text-muted small">
                              {getSchemeName(referral.priceScheme)}
                            </div>
                          </div>
                        ) : (
                          <Badge bg="warning">Not Assigned</Badge>
                        )}
                      </td>
                      <td>{referral.discountPercentage}%</td>
                      <td>
                        <Badge bg={referral.isActive ? 'success' : 'danger'}>
                          {referral.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </td>
                      <td>
                        <Button
                          variant="primary"
                          size="sm"
                          onClick={() => handleAssignClick(referral, referral.category)}
                          disabled={!referral.isActive}
                        >
                          <FontAwesomeIcon icon={faUserCog} className="me-1" />
                          {referral.priceScheme ? 'Change' : 'Assign'}
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Price Scheme Assignment Modal */}
      <Modal show={showAssignModal} onHide={() => setShowAssignModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>
            <FontAwesomeIcon icon={faDollarSign} className="me-2" />
            Assign Price Scheme
          </Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleAssignSubmit}>
          <Modal.Body>
            {selectedReferral && (
              <>
                <Alert variant="info">
                  <strong>Referral Entity:</strong> {selectedReferral.name} ({selectedReferral.code})
                  <br />
                  <strong>Category:</strong> {categoryConfig[selectedCategory]?.title}
                </Alert>

                <Form.Group className="mb-3">
                  <Form.Label>Select Price Scheme *</Form.Label>
                  <Form.Select
                    value={selectedScheme}
                    onChange={(e) => setSelectedScheme(e.target.value)}
                    required
                  >
                    <option value="">Select Price Scheme</option>
                    {priceSchemes.map(scheme => (
                      <option key={scheme.id} value={scheme.code}>
                        {scheme.name} ({scheme.code}) - {scheme.description}
                      </option>
                    ))}
                  </Form.Select>
                  <Form.Text className="text-muted">
                    This price scheme will be used for automatic price calculation when this referral is selected in billing.
                  </Form.Text>
                </Form.Group>

                {selectedScheme && (
                  <Alert variant="success">
                    <FontAwesomeIcon icon={faCheck} className="me-2" />
                    Price scheme "{getSchemeName(selectedScheme)}" will be assigned to this referral entity.
                  </Alert>
                )}
              </>
            )}
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowAssignModal(false)}>
              Cancel
            </Button>
            <Button variant="primary" type="submit" disabled={loading || !selectedScheme}>
              {loading ? 'Assigning...' : 'Assign Price Scheme'}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    </div>
  );
};

export default PriceSchemeAssignment;
