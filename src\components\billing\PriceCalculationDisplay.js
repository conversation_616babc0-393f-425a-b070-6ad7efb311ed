import React from 'react';
import {
  Card,
  Table,
  Badge,
  Alert
} from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faDollarSign,
  faCalculator,
  faInfoCircle,
  faArrowDown,
  faArrowUp,
  faEquals
} from '@fortawesome/free-solid-svg-icons';

const PriceCalculationDisplay = ({ 
  pricingBreakdown, 
  showDetails = true, 
  compact = false 
}) => {
  if (!pricingBreakdown) {
    return null;
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const getSourceBadgeColor = (source) => {
    switch (source) {
      case 'price_scheme':
        return 'success';
      case 'referral_discount':
        return 'primary';
      case 'base_with_discount':
        return 'warning';
      case 'fallback':
        return 'danger';
      default:
        return 'secondary';
    }
  };

  const getSourceDescription = (source) => {
    switch (source) {
      case 'price_scheme':
        return 'Price calculated using assigned price scheme';
      case 'referral_discount':
        return 'Base price with referral discount applied';
      case 'base_with_discount':
        return 'Base price with discount (test not in scheme)';
      case 'fallback':
        return 'Fallback pricing due to calculation error';
      default:
        return 'Standard pricing';
    }
  };

  const getStepIcon = (step) => {
    if (step.includes('Discount') || step.includes('discount')) {
      return faArrowDown;
    } else if (step.includes('Final') || step.includes('final')) {
      return faEquals;
    } else {
      return faArrowUp;
    }
  };

  const getStepColor = (amount) => {
    if (amount < 0) {
      return 'text-success'; // Discount/reduction
    } else if (amount > 0) {
      return 'text-primary'; // Positive amount
    } else {
      return 'text-muted'; // Zero amount
    }
  };

  if (compact) {
    return (
      <div className="price-calculation-compact">
        <div className="d-flex justify-content-between align-items-center">
          <div>
            <strong className="text-success">{formatCurrency(pricingBreakdown.finalPrice)}</strong>
            {pricingBreakdown.discountAmount > 0 && (
              <small className="text-muted ms-2">
                (was {formatCurrency(pricingBreakdown.basePrice)})
              </small>
            )}
          </div>
          <Badge bg={getSourceBadgeColor(pricingBreakdown.source)}>
            {pricingBreakdown.priceScheme || 'Standard'}
          </Badge>
        </div>
        {pricingBreakdown.discountPercentage > 0 && (
          <small className="text-success">
            {pricingBreakdown.discountPercentage}% discount applied
          </small>
        )}
      </div>
    );
  }

  return (
    <Card className="price-calculation-display">
      <Card.Header className="py-2">
        <div className="d-flex justify-content-between align-items-center">
          <div>
            <FontAwesomeIcon icon={faCalculator} className="me-2" />
            <strong>Price Calculation</strong>
          </div>
          <Badge bg={getSourceBadgeColor(pricingBreakdown.source)}>
            {getSourceDescription(pricingBreakdown.source)}
          </Badge>
        </div>
      </Card.Header>

      <Card.Body className="py-2">
        {/* Summary Information */}
        <div className="row mb-3">
          <div className="col-md-6">
            <div className="small text-muted">Referral Source</div>
            <div><strong>{pricingBreakdown.referralName}</strong></div>
            {pricingBreakdown.category && (
              <Badge bg="secondary" className="mt-1">
                {pricingBreakdown.category}
              </Badge>
            )}
          </div>
          <div className="col-md-6 text-end">
            <div className="small text-muted">Final Price</div>
            <div className="h4 text-success mb-0">
              <FontAwesomeIcon icon={faDollarSign} className="me-1" />
              {formatCurrency(pricingBreakdown.finalPrice)}
            </div>
          </div>
        </div>

        {/* Price Scheme Information */}
        {pricingBreakdown.priceScheme && (
          <Alert variant="info" className="py-2 mb-3">
            <FontAwesomeIcon icon={faInfoCircle} className="me-2" />
            <strong>Price Scheme:</strong> {pricingBreakdown.priceScheme}
            {pricingBreakdown.discountPercentage > 0 && (
              <span className="ms-2">
                + {pricingBreakdown.discountPercentage}% Additional Discount
              </span>
            )}
          </Alert>
        )}

        {/* Detailed Calculation Steps */}
        {showDetails && pricingBreakdown.calculations && pricingBreakdown.calculations.length > 0 && (
          <div>
            <div className="small text-muted mb-2">
              <FontAwesomeIcon icon={faCalculator} className="me-1" />
              Calculation Breakdown
            </div>
            <Table size="sm" className="mb-0">
              <tbody>
                {pricingBreakdown.calculations.map((calc, index) => (
                  <tr key={index}>
                    <td className="border-0 py-1">
                      <FontAwesomeIcon 
                        icon={getStepIcon(calc.step)} 
                        className={`me-2 ${getStepColor(calc.amount)}`}
                      />
                      {calc.step}
                    </td>
                    <td className="border-0 py-1 text-end">
                      <span className={getStepColor(calc.amount)}>
                        {calc.amount >= 0 ? '+' : ''}{formatCurrency(calc.amount)}
                      </span>
                    </td>
                    <td className="border-0 py-1 text-muted small">
                      {calc.description}
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </div>
        )}

        {/* Savings Information */}
        {pricingBreakdown.discountAmount > 0 && (
          <div className="mt-3 p-2 bg-light rounded">
            <div className="d-flex justify-content-between align-items-center">
              <span className="text-success">
                <FontAwesomeIcon icon={faArrowDown} className="me-1" />
                <strong>You Save:</strong>
              </span>
              <span className="text-success h6 mb-0">
                {formatCurrency(pricingBreakdown.discountAmount)}
              </span>
            </div>
            <small className="text-muted">
              {((pricingBreakdown.discountAmount / pricingBreakdown.basePrice) * 100).toFixed(1)}% 
              savings from original price
            </small>
          </div>
        )}
      </Card.Body>
    </Card>
  );
};

export default PriceCalculationDisplay;
