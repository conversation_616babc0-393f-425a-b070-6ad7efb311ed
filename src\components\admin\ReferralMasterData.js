import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Row,
  Col,
  Alert,
  Badge,
  Tabs,
  Tab,
  InputGroup,
  Spinner
} from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPlus,
  faEdit,
  faTrash,
  faSync,
  faSearch,
  faUsers,
  faUserMd,
  faHospital,
  faBuilding,
  faShieldAlt,
  faFlask,
  faDollarSign
} from '@fortawesome/free-solid-svg-icons';
import { adminAPI } from '../../services/api';

const ReferralMasterData = () => {
  // State management
  const [activeTab, setActiveTab] = useState('labMaster');
  const [data, setData] = useState({
    labMaster: [],
    doctorMaster: [],
    hospitalMaster: [],
    corporateMaster: [],
    insuranceMaster: [],
    patientMaster: []
  });
  const [priceSchemes, setPriceSchemes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  
  // Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  // Form data
  const [formData, setFormData] = useState({
    code: '',
    name: '',
    description: '',
    contactPerson: '',
    contactNumber: '',
    email: '',
    address: '',
    city: '',
    state: '',
    pincode: '',
    gstNumber: '',
    panNumber: '',
    priceScheme: '',
    discountPercentage: 0,
    commissionPercentage: 0,
    creditLimit: 0,
    creditDays: 0,
    isActive: true,
    remarks: ''
  });

  // Category configurations
  const categoryConfig = {
    labMaster: {
      title: 'Lab Master',
      icon: faFlask,
      description: 'Manage laboratory referral entities',
      specificFields: ['labType', 'accreditation', 'testCapabilities', 'equipmentList']
    },
    doctorMaster: {
      title: 'Doctor Master',
      icon: faUserMd,
      description: 'Manage doctor referral entities',
      specificFields: ['qualification', 'specialization', 'registrationNumber', 'consultationFee']
    },
    hospitalMaster: {
      title: 'Hospital Master',
      icon: faHospital,
      description: 'Manage hospital referral entities',
      specificFields: ['hospitalType', 'bedCapacity', 'accreditation', 'emergencyServices']
    },
    corporateMaster: {
      title: 'Corporate Master',
      icon: faBuilding,
      description: 'Manage corporate referral entities',
      specificFields: ['companyType', 'employeeCount', 'industry', 'contractStartDate', 'contractEndDate']
    },
    insuranceMaster: {
      title: 'Insurance Master',
      icon: faShieldAlt,
      description: 'Manage insurance referral entities',
      specificFields: ['insuranceType', 'policyNumber', 'coverageAmount', 'validFrom', 'validTo']
    },
    patientMaster: {
      title: 'Patient Master',
      icon: faUsers,
      description: 'Manage patient referral entities',
      specificFields: []
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchAllData();
    fetchPriceSchemes();
  }, []);

  const fetchAllData = async () => {
    setLoading(true);
    try {
      const response = await adminAPI.get('/admin/referral-master-data');
      if (response.data.success) {
        setData(response.data.data);
      } else {
        setError('Failed to load referral master data');
      }
    } catch (err) {
      console.error('Error fetching referral master data:', err);
      setError('Failed to load referral master data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchPriceSchemes = async () => {
    try {
      const response = await adminAPI.get('/admin/price-schemes-for-referral');
      if (response.data.success) {
        setPriceSchemes(response.data.data);
      }
    } catch (err) {
      console.error('Error fetching price schemes:', err);
    }
  };

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };

  const getFilteredData = () => {
    const currentData = data[activeTab] || [];
    if (!searchQuery) return currentData;
    
    return currentData.filter(item =>
      item.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.code?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const resetForm = () => {
    setFormData({
      code: '',
      name: '',
      description: '',
      contactPerson: '',
      contactNumber: '',
      email: '',
      address: '',
      city: '',
      state: '',
      pincode: '',
      gstNumber: '',
      panNumber: '',
      priceScheme: '',
      discountPercentage: 0,
      commissionPercentage: 0,
      creditLimit: 0,
      creditDays: 0,
      isActive: true,
      remarks: ''
    });
  };

  const handleAddClick = () => {
    resetForm();
    setShowAddModal(true);
  };

  const handleEditClick = (item) => {
    setSelectedItem(item);
    setFormData({ ...item });
    setShowEditModal(true);
  };

  const handleDeleteClick = (item) => {
    setSelectedItem(item);
    setShowDeleteModal(true);
  };

  const handleFormChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      const response = await adminAPI.post(`/admin/referral-master-data/${activeTab}`, formData);
      if (response.data.success) {
        await fetchAllData();
        setShowAddModal(false);
        resetForm();
        setError('');
      } else {
        setError(response.data.message || 'Failed to add item');
      }
    } catch (err) {
      console.error('Error adding item:', err);
      setError('Failed to add item. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdate = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      const response = await adminAPI.put(`/admin/referral-master-data/${activeTab}/${selectedItem.id}`, formData);
      if (response.data.success) {
        await fetchAllData();
        setShowEditModal(false);
        setSelectedItem(null);
        resetForm();
        setError('');
      } else {
        setError(response.data.message || 'Failed to update item');
      }
    } catch (err) {
      console.error('Error updating item:', err);
      setError('Failed to update item. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    setLoading(true);
    
    try {
      const response = await adminAPI.delete(`/admin/referral-master-data/${activeTab}/${selectedItem.id}`);
      if (response.data.success) {
        await fetchAllData();
        setShowDeleteModal(false);
        setSelectedItem(null);
        setError('');
      } else {
        setError(response.data.message || 'Failed to delete item');
      }
    } catch (err) {
      console.error('Error deleting item:', err);
      setError('Failed to delete item. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="referral-master-data">
      <Card>
        <Card.Header>
          <Tabs
            activeKey={activeTab}
            onSelect={setActiveTab}
            className="mb-0"
          >
            {Object.entries(categoryConfig).map(([key, config]) => (
              <Tab
                key={key}
                eventKey={key}
                title={
                  <>
                    <FontAwesomeIcon icon={config.icon} className="me-2" />
                    {config.title}
                  </>
                }
              />
            ))}
          </Tabs>
        </Card.Header>

        <div className="card-header-search py-2 px-3 border-bottom">
          <Row className="align-items-center">
            <Col md={6}>
              <InputGroup>
                <Form.Control
                  type="text"
                  placeholder="Search..."
                  value={searchQuery}
                  onChange={handleSearch}
                />
                <Button variant="outline-secondary">
                  <FontAwesomeIcon icon={faSearch} />
                </Button>
              </InputGroup>
            </Col>
            <Col md={6} className="text-end">
              <Button variant="success" onClick={handleAddClick} className="me-2">
                <FontAwesomeIcon icon={faPlus} className="me-1" />
                Add New
              </Button>
              <Button variant="outline-primary" onClick={fetchAllData}>
                <FontAwesomeIcon icon={faSync} className="me-1" />
                Refresh
              </Button>
            </Col>
          </Row>
        </div>

        <Card.Body>
          {error && <Alert variant="danger">{error}</Alert>}
          
          <Alert variant="info" className="mb-3">
            <FontAwesomeIcon icon={categoryConfig[activeTab].icon} className="me-2" />
            <strong>{categoryConfig[activeTab].title}:</strong> {categoryConfig[activeTab].description}
          </Alert>

          {loading ? (
            <div className="text-center py-4">
              <Spinner animation="border" role="status">
                <span className="visually-hidden">Loading...</span>
              </Spinner>
            </div>
          ) : (
            <div className="table-responsive">
              <Table className="table-hover">
                <thead>
                  <tr>
                    <th>Code</th>
                    <th>Name</th>
                    <th>Contact</th>
                    <th>Price Scheme</th>
                    <th>Discount %</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {getFilteredData().map(item => (
                    <tr key={item.id}>
                      <td>{item.code}</td>
                      <td>
                        <div>
                          <strong>{item.name}</strong>
                          {item.description && (
                            <div className="text-muted small">{item.description}</div>
                          )}
                        </div>
                      </td>
                      <td>
                        <div>
                          {item.contactPerson && <div>{item.contactPerson}</div>}
                          {item.contactNumber && <div className="text-muted small">{item.contactNumber}</div>}
                        </div>
                      </td>
                      <td>
                        {item.priceScheme ? (
                          <Badge bg="primary">{item.priceScheme}</Badge>
                        ) : (
                          <Badge bg="secondary">Not Assigned</Badge>
                        )}
                      </td>
                      <td>{item.discountPercentage}%</td>
                      <td>
                        <Badge bg={item.isActive ? 'success' : 'danger'}>
                          {item.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </td>
                      <td>
                        <Button
                          variant="primary"
                          size="sm"
                          className="me-1"
                          onClick={() => handleEditClick(item)}
                        >
                          <FontAwesomeIcon icon={faEdit} />
                        </Button>
                        <Button
                          variant="danger"
                          size="sm"
                          onClick={() => handleDeleteClick(item)}
                        >
                          <FontAwesomeIcon icon={faTrash} />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Add Modal */}
      <Modal show={showAddModal} onHide={() => setShowAddModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <FontAwesomeIcon icon={categoryConfig[activeTab].icon} className="me-2" />
            Add New {categoryConfig[activeTab].title}
          </Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleSubmit}>
          <Modal.Body>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Code *</Form.Label>
                  <Form.Control
                    type="text"
                    name="code"
                    value={formData.code}
                    onChange={handleFormChange}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Name *</Form.Label>
                  <Form.Control
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleFormChange}
                    required
                  />
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={2}
                name="description"
                value={formData.description}
                onChange={handleFormChange}
              />
            </Form.Group>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Contact Person</Form.Label>
                  <Form.Control
                    type="text"
                    name="contactPerson"
                    value={formData.contactPerson}
                    onChange={handleFormChange}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Contact Number</Form.Label>
                  <Form.Control
                    type="text"
                    name="contactNumber"
                    value={formData.contactNumber}
                    onChange={handleFormChange}
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Email</Form.Label>
                  <Form.Control
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleFormChange}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Price Scheme</Form.Label>
                  <Form.Select
                    name="priceScheme"
                    value={formData.priceScheme}
                    onChange={handleFormChange}
                  >
                    <option value="">Select Price Scheme</option>
                    {priceSchemes.map(scheme => (
                      <option key={scheme.id} value={scheme.code}>
                        {scheme.name} ({scheme.code})
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Address</Form.Label>
              <Form.Control
                as="textarea"
                rows={2}
                name="address"
                value={formData.address}
                onChange={handleFormChange}
              />
            </Form.Group>

            <Row>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>City</Form.Label>
                  <Form.Control
                    type="text"
                    name="city"
                    value={formData.city}
                    onChange={handleFormChange}
                  />
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>State</Form.Label>
                  <Form.Control
                    type="text"
                    name="state"
                    value={formData.state}
                    onChange={handleFormChange}
                  />
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Pincode</Form.Label>
                  <Form.Control
                    type="text"
                    name="pincode"
                    value={formData.pincode}
                    onChange={handleFormChange}
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>GST Number</Form.Label>
                  <Form.Control
                    type="text"
                    name="gstNumber"
                    value={formData.gstNumber}
                    onChange={handleFormChange}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>PAN Number</Form.Label>
                  <Form.Control
                    type="text"
                    name="panNumber"
                    value={formData.panNumber}
                    onChange={handleFormChange}
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Discount %</Form.Label>
                  <Form.Control
                    type="number"
                    step="0.01"
                    name="discountPercentage"
                    value={formData.discountPercentage}
                    onChange={handleFormChange}
                  />
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Commission %</Form.Label>
                  <Form.Control
                    type="number"
                    step="0.01"
                    name="commissionPercentage"
                    value={formData.commissionPercentage}
                    onChange={handleFormChange}
                  />
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Credit Limit</Form.Label>
                  <Form.Control
                    type="number"
                    name="creditLimit"
                    value={formData.creditLimit}
                    onChange={handleFormChange}
                  />
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Credit Days</Form.Label>
                  <Form.Control
                    type="number"
                    name="creditDays"
                    value={formData.creditDays}
                    onChange={handleFormChange}
                  />
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Remarks</Form.Label>
              <Form.Control
                as="textarea"
                rows={2}
                name="remarks"
                value={formData.remarks}
                onChange={handleFormChange}
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                name="isActive"
                label="Active"
                checked={formData.isActive}
                onChange={handleFormChange}
              />
            </Form.Group>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowAddModal(false)}>
              Cancel
            </Button>
            <Button variant="primary" type="submit" disabled={loading}>
              {loading ? 'Adding...' : 'Add'}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>

      {/* Edit Modal */}
      <Modal show={showEditModal} onHide={() => setShowEditModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <FontAwesomeIcon icon={categoryConfig[activeTab].icon} className="me-2" />
            Edit {categoryConfig[activeTab].title}
          </Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleUpdate}>
          <Modal.Body>
            {/* Same form fields as Add Modal */}
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Code *</Form.Label>
                  <Form.Control
                    type="text"
                    name="code"
                    value={formData.code}
                    onChange={handleFormChange}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Name *</Form.Label>
                  <Form.Control
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleFormChange}
                    required
                  />
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={2}
                name="description"
                value={formData.description}
                onChange={handleFormChange}
              />
            </Form.Group>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Contact Person</Form.Label>
                  <Form.Control
                    type="text"
                    name="contactPerson"
                    value={formData.contactPerson}
                    onChange={handleFormChange}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Contact Number</Form.Label>
                  <Form.Control
                    type="text"
                    name="contactNumber"
                    value={formData.contactNumber}
                    onChange={handleFormChange}
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Email</Form.Label>
                  <Form.Control
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleFormChange}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Price Scheme</Form.Label>
                  <Form.Select
                    name="priceScheme"
                    value={formData.priceScheme}
                    onChange={handleFormChange}
                  >
                    <option value="">Select Price Scheme</option>
                    {priceSchemes.map(scheme => (
                      <option key={scheme.id} value={scheme.code}>
                        {scheme.name} ({scheme.code})
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                name="isActive"
                label="Active"
                checked={formData.isActive}
                onChange={handleFormChange}
              />
            </Form.Group>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowEditModal(false)}>
              Cancel
            </Button>
            <Button variant="primary" type="submit" disabled={loading}>
              {loading ? 'Updating...' : 'Update'}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Confirm Delete</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          Are you sure you want to delete "{selectedItem?.name}"?
          <br />
          <small className="text-muted">This action will mark the item as inactive.</small>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button variant="danger" onClick={handleDelete} disabled={loading}>
            {loading ? 'Deleting...' : 'Delete'}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default ReferralMasterData;
