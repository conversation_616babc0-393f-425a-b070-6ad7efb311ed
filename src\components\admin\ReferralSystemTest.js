import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  But<PERSON>,
  Alert,
  Table,
  Badge,
  Spinner,
  Row,
  Col
} from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCheckCircle,
  faTimesCircle,
  faSpinner,
  faPlay,
  faInfoCircle
} from '@fortawesome/free-solid-svg-icons';
import { adminAPI } from '../../services/api';
import { referralMasterService } from '../../services/referralMasterService';

const ReferralSystemTest = () => {
  const [testResults, setTestResults] = useState([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState('');

  const tests = [
    {
      id: 'load_referral_data',
      name: 'Load Referral Master Data',
      description: 'Test loading all referral categories'
    },
    {
      id: 'load_price_schemes',
      name: 'Load Price Schemes',
      description: 'Test loading price schemes including L2L'
    },
    {
      id: 'test_l2l_scheme',
      name: 'Verify L2L Scheme',
      description: 'Verify L2L scheme is available and has test data'
    },
    {
      id: 'test_referral_pricing',
      name: 'Test Referral Pricing Calculation',
      description: 'Test price calculation for a lab referral'
    },
    {
      id: 'test_cascading_dropdowns',
      name: 'Test Cascading Dropdowns',
      description: 'Test enhanced referral source selection'
    },
    {
      id: 'test_price_assignment',
      name: 'Test Price Scheme Assignment',
      description: 'Test assigning L2L scheme to a lab'
    },
    {
      id: 'debug_integration',
      name: 'Debug Integration',
      description: 'Run comprehensive integration debug test'
    }
  ];

  const runTest = async (testId) => {
    setCurrentTest(testId);
    const startTime = Date.now();
    
    try {
      let result = { success: false, message: '', data: null };
      
      switch (testId) {
        case 'load_referral_data':
          result = await testLoadReferralData();
          break;
        case 'load_price_schemes':
          result = await testLoadPriceSchemes();
          break;
        case 'test_l2l_scheme':
          result = await testL2LScheme();
          break;
        case 'test_referral_pricing':
          result = await testReferralPricing();
          break;
        case 'test_cascading_dropdowns':
          result = await testCascadingDropdowns();
          break;
        case 'test_price_assignment':
          result = await testPriceAssignment();
          break;
        case 'debug_integration':
          result = await debugIntegration();
          break;
        default:
          result = { success: false, message: 'Unknown test' };
      }
      
      const duration = Date.now() - startTime;
      
      setTestResults(prev => [...prev, {
        ...result,
        testId,
        duration,
        timestamp: new Date().toLocaleTimeString()
      }]);
      
    } catch (error) {
      const duration = Date.now() - startTime;
      setTestResults(prev => [...prev, {
        success: false,
        message: `Test failed: ${error.message}`,
        testId,
        duration,
        timestamp: new Date().toLocaleTimeString()
      }]);
    }
  };

  const testLoadReferralData = async () => {
    const response = await adminAPI.get('/admin/referral-master-data');
    if (response.data.success) {
      const data = response.data.data;
      const totalItems = Object.values(data).reduce((sum, items) => sum + items.length, 0);
      return {
        success: true,
        message: `Successfully loaded ${totalItems} referral entities across ${Object.keys(data).length} categories`,
        data: { totalItems, categories: Object.keys(data) }
      };
    } else {
      return { success: false, message: response.data.message || 'Failed to load referral data' };
    }
  };

  const testLoadPriceSchemes = async () => {
    const response = await adminAPI.get('/admin/price-schemes-for-referral');
    if (response.data.success) {
      const schemes = response.data.data;
      return {
        success: true,
        message: `Successfully loaded ${schemes.length} price schemes`,
        data: { schemes: schemes.map(s => ({ code: s.code, name: s.name, source: s.source })) }
      };
    } else {
      return { success: false, message: response.data.message || 'Failed to load price schemes' };
    }
  };

  const testL2LScheme = async () => {
    const response = await adminAPI.get('/admin/price-schemes-for-referral');
    if (response.data.success) {
      const schemes = response.data.data;
      const l2lScheme = schemes.find(s => s.code === '@000002' && s.name === 'L2L');
      
      if (l2lScheme) {
        return {
          success: true,
          message: `L2L scheme found: ${l2lScheme.name} (${l2lScheme.code}) from ${l2lScheme.source}`,
          data: l2lScheme
        };
      } else {
        return { success: false, message: 'L2L scheme not found in available schemes' };
      }
    } else {
      return { success: false, message: 'Failed to load price schemes for L2L verification' };
    }
  };

  const testReferralPricing = async () => {
    // Test with the first lab in our sample data
    const labId = 1;
    const response = await adminAPI.get(`/admin/referral-pricing/${labId}`);
    
    if (response.data.success) {
      const pricing = response.data.data;
      return {
        success: true,
        message: `Pricing loaded for ${pricing.referralName}: ${pricing.testPrices.length} test prices`,
        data: {
          referralName: pricing.referralName,
          priceScheme: pricing.priceScheme,
          discountPercentage: pricing.discountPercentage,
          testPricesCount: pricing.testPrices.length
        }
      };
    } else {
      return { success: false, message: response.data.message || 'Failed to load referral pricing' };
    }
  };

  const testCascadingDropdowns = async () => {
    const enhancedSources = await referralMasterService.getEnhancedReferralSources();
    
    if (enhancedSources && enhancedSources.categories) {
      const totalEntities = Object.values(enhancedSources.entities).reduce((sum, items) => sum + items.length, 0);
      return {
        success: true,
        message: `Enhanced sources loaded: ${enhancedSources.categories.length} categories, ${totalEntities} entities`,
        data: {
          categories: enhancedSources.categories.length,
          totalEntities,
          categoriesWithEntities: Object.keys(enhancedSources.entities).filter(cat => enhancedSources.entities[cat].length > 0)
        }
      };
    } else {
      return { success: false, message: 'Failed to load enhanced referral sources' };
    }
  };

  const testPriceAssignment = async () => {
    // Test assigning L2L scheme to the first lab
    const assignmentData = {
      category: 'labMaster',
      referralId: 1,
      priceScheme: '@000002'
    };
    
    const response = await adminAPI.post('/admin/assign-price-scheme', assignmentData);
    
    if (response.data.success) {
      return {
        success: true,
        message: `Successfully assigned L2L scheme to lab entity`,
        data: assignmentData
      };
    } else {
      return { success: false, message: response.data.message || 'Failed to assign price scheme' };
    }
  };

  const debugIntegration = async () => {
    const response = await adminAPI.get('/admin/debug-referral-integration');

    if (response.data.success) {
      const debugData = response.data.data;
      const failedTests = debugData.tests.filter(t => !t.success);

      return {
        success: failedTests.length === 0,
        message: failedTests.length === 0
          ? `All ${debugData.tests.length} integration tests passed`
          : `${failedTests.length} of ${debugData.tests.length} tests failed`,
        data: {
          timestamp: debugData.timestamp,
          totalTests: debugData.tests.length,
          passedTests: debugData.tests.filter(t => t.success).length,
          failedTests: failedTests.length,
          details: debugData.tests
        }
      };
    } else {
      return { success: false, message: response.data.message || 'Debug integration test failed' };
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    setCurrentTest('');
    
    for (const test of tests) {
      await runTest(test.id);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    setIsRunning(false);
    setCurrentTest('');
  };

  const getTestStatus = (testId) => {
    const result = testResults.find(r => r.testId === testId);
    if (!result) {
      return currentTest === testId ? 'running' : 'pending';
    }
    return result.success ? 'success' : 'failed';
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success':
        return <FontAwesomeIcon icon={faCheckCircle} className="text-success" />;
      case 'failed':
        return <FontAwesomeIcon icon={faTimesCircle} className="text-danger" />;
      case 'running':
        return <FontAwesomeIcon icon={faSpinner} className="text-primary" spin />;
      default:
        return <FontAwesomeIcon icon={faInfoCircle} className="text-muted" />;
    }
  };

  return (
    <div className="referral-system-test">
      <Card>
        <Card.Header>
          <div className="d-flex justify-content-between align-items-center">
            <h5 className="mb-0">
              <FontAwesomeIcon icon={faPlay} className="me-2" />
              Referral System Integration Test
            </h5>
            <Button 
              variant="primary" 
              onClick={runAllTests}
              disabled={isRunning}
            >
              {isRunning ? (
                <>
                  <Spinner animation="border" size="sm" className="me-2" />
                  Running Tests...
                </>
              ) : (
                'Run All Tests'
              )}
            </Button>
          </div>
        </Card.Header>

        <Card.Body>
          <Alert variant="info" className="mb-4">
            <FontAwesomeIcon icon={faInfoCircle} className="me-2" />
            This test suite verifies the complete referral master data system integration including:
            CRUD operations, L2L price scheme integration, cascading dropdowns, and dynamic pricing.
          </Alert>

          <Table className="table-hover">
            <thead>
              <tr>
                <th width="50">Status</th>
                <th>Test Name</th>
                <th>Description</th>
                <th width="100">Duration</th>
                <th width="100">Time</th>
              </tr>
            </thead>
            <tbody>
              {tests.map(test => {
                const status = getTestStatus(test.id);
                const result = testResults.find(r => r.testId === test.id);
                
                return (
                  <tr key={test.id}>
                    <td className="text-center">
                      {getStatusIcon(status)}
                    </td>
                    <td>
                      <strong>{test.name}</strong>
                      {result && !result.success && (
                        <div className="text-danger small mt-1">
                          {result.message}
                        </div>
                      )}
                      {result && result.success && result.data && (
                        <div className="text-success small mt-1">
                          {result.message}
                        </div>
                      )}
                    </td>
                    <td className="text-muted">{test.description}</td>
                    <td>
                      {result && (
                        <Badge bg={result.success ? 'success' : 'danger'}>
                          {result.duration}ms
                        </Badge>
                      )}
                    </td>
                    <td className="small text-muted">
                      {result?.timestamp}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </Table>

          {testResults.length > 0 && (
            <Row className="mt-4">
              <Col md={12}>
                <div className="d-flex justify-content-between">
                  <div>
                    <Badge bg="success" className="me-2">
                      Passed: {testResults.filter(r => r.success).length}
                    </Badge>
                    <Badge bg="danger">
                      Failed: {testResults.filter(r => !r.success).length}
                    </Badge>
                  </div>
                  <div className="text-muted">
                    Total Duration: {testResults.reduce((sum, r) => sum + r.duration, 0)}ms
                  </div>
                </div>
              </Col>
            </Row>
          )}
        </Card.Body>
      </Card>
    </div>
  );
};

export default ReferralSystemTest;
