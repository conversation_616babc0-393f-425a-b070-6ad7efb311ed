#!/usr/bin/env python3
"""
Test script for Referral Master Data API endpoints
"""

import requests
import json
import sys
import os

# Configuration
BASE_URL = "http://localhost:5000"
API_BASE = f"{BASE_URL}/api/admin"

def test_get_referral_master_data():
    """Test getting all referral master data"""
    print("Testing GET /admin/referral-master-data...")
    
    try:
        response = requests.get(f"{API_BASE}/referral-master-data")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data.get('success', False)}")
            if data.get('success'):
                for category, items in data.get('data', {}).items():
                    print(f"  {category}: {len(items)} items")
            else:
                print(f"Error: {data.get('message', 'Unknown error')}")
        else:
            print(f"HTTP Error: {response.text}")
            
    except Exception as e:
        print(f"Exception: {e}")

def test_get_price_schemes():
    """Test getting price schemes for referral"""
    print("\nTesting GET /admin/price-schemes-for-referral...")
    
    try:
        response = requests.get(f"{API_BASE}/price-schemes-for-referral")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data.get('success', False)}")
            if data.get('success'):
                schemes = data.get('data', [])
                print(f"  Found {len(schemes)} price schemes")
                for scheme in schemes[:5]:  # Show first 5
                    print(f"    - {scheme.get('code', 'N/A')}: {scheme.get('name', 'N/A')} ({scheme.get('source', 'N/A')})")
                if len(schemes) > 5:
                    print(f"    ... and {len(schemes) - 5} more")
            else:
                print(f"Error: {data.get('message', 'Unknown error')}")
        else:
            print(f"HTTP Error: {response.text}")
            
    except Exception as e:
        print(f"Exception: {e}")

def test_add_referral_item():
    """Test adding a new referral item"""
    print("\nTesting POST /admin/referral-master-data/labMaster...")
    
    test_data = {
        "code": "TEST001",
        "name": "Test Lab",
        "description": "Test laboratory for API testing",
        "contactPerson": "Test Contact",
        "contactNumber": "+91-9999999999",
        "email": "<EMAIL>",
        "priceScheme": "@000002",
        "discountPercentage": 10.0,
        "isActive": True,
        "labType": "Diagnostic",
        "accreditation": "NABL"
    }
    
    try:
        response = requests.post(f"{API_BASE}/referral-master-data/labMaster", json=test_data)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 201:
            data = response.json()
            print(f"Success: {data.get('success', False)}")
            if data.get('success'):
                item = data.get('data', {})
                print(f"  Created item with ID: {item.get('id', 'N/A')}")
                print(f"  Name: {item.get('name', 'N/A')}")
                print(f"  Price Scheme: {item.get('priceScheme', 'N/A')}")
                return item.get('id')
            else:
                print(f"Error: {data.get('message', 'Unknown error')}")
        else:
            print(f"HTTP Error: {response.text}")
            
    except Exception as e:
        print(f"Exception: {e}")
    
    return None

def test_assign_price_scheme(referral_id):
    """Test assigning price scheme to referral"""
    if not referral_id:
        print("\nSkipping price scheme assignment test (no referral ID)")
        return
        
    print(f"\nTesting POST /admin/assign-price-scheme for referral ID {referral_id}...")
    
    assignment_data = {
        "category": "labMaster",
        "referralId": referral_id,
        "priceScheme": "@000002"
    }
    
    try:
        response = requests.post(f"{API_BASE}/assign-price-scheme", json=assignment_data)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data.get('success', False)}")
            if data.get('success'):
                print(f"  Message: {data.get('message', 'N/A')}")
            else:
                print(f"Error: {data.get('message', 'Unknown error')}")
        else:
            print(f"HTTP Error: {response.text}")
            
    except Exception as e:
        print(f"Exception: {e}")

def test_get_referral_pricing(referral_id):
    """Test getting referral pricing"""
    if not referral_id:
        print("\nSkipping referral pricing test (no referral ID)")
        return
        
    print(f"\nTesting GET /admin/referral-pricing/{referral_id}...")
    
    try:
        response = requests.get(f"{API_BASE}/referral-pricing/{referral_id}")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data.get('success', False)}")
            if data.get('success'):
                pricing = data.get('data', {})
                print(f"  Referral: {pricing.get('referralName', 'N/A')}")
                print(f"  Category: {pricing.get('category', 'N/A')}")
                print(f"  Price Scheme: {pricing.get('priceScheme', 'N/A')}")
                print(f"  Discount: {pricing.get('discountPercentage', 0)}%")
                print(f"  Test Prices: {len(pricing.get('testPrices', []))} items")
            else:
                print(f"Error: {data.get('message', 'Unknown error')}")
        else:
            print(f"HTTP Error: {response.text}")
            
    except Exception as e:
        print(f"Exception: {e}")

def cleanup_test_data(referral_id):
    """Clean up test data"""
    if not referral_id:
        return
        
    print(f"\nCleaning up test data (ID: {referral_id})...")
    
    try:
        response = requests.delete(f"{API_BASE}/referral-master-data/labMaster/{referral_id}")
        print(f"Cleanup Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Cleanup Success: {data.get('success', False)}")
        else:
            print(f"Cleanup Error: {response.text}")
            
    except Exception as e:
        print(f"Cleanup Exception: {e}")

def main():
    """Main test function"""
    print("=== Referral Master Data API Test ===\n")
    
    # Test basic data retrieval
    test_get_referral_master_data()
    test_get_price_schemes()
    
    # Test CRUD operations
    referral_id = test_add_referral_item()
    test_assign_price_scheme(referral_id)
    test_get_referral_pricing(referral_id)
    
    # Cleanup
    cleanup_test_data(referral_id)
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    main()
